======================
``Qlib`` Documentation
======================

``Qlib`` is an AI-oriented quantitative investment platform, which aims to realize the potential, empower the research, and create the value of AI technologies in quantitative investment.

.. _user_guide:

Document Structure
====================

.. toctree::
   :hidden:

   Home <self>

.. toctree::
   :maxdepth: 3
   :caption: GETTING STARTED:

   Introduction <introduction/introduction.rst>
   Quick Start <introduction/quick.rst>

.. toctree::
   :maxdepth: 3
   :caption: FIRST STEPS:

   Installation <start/installation.rst>
   Initialization <start/initialization.rst>
   Data Retrieval <start/getdata.rst>
   Custom Model Integration <start/integration.rst>


.. toctree::
   :maxdepth: 3
   :caption: MAIN COMPONENTS:

   Workflow: Workflow Management <component/workflow.rst>
   Data Layer: Data Framework & Usage <component/data.rst>
   Forecast Model: Model Training & Prediction <component/model.rst>
   Portfolio Management and Backtest <component/strategy.rst>
   Nested Decision Execution: High-Frequency Trading <component/highfreq.rst>
   Meta Controller: Meta-Task & Meta-Dataset & Meta-Model <component/meta.rst>
   Qlib Recorder: Experiment Management <component/recorder.rst>
   Analysis: Evaluation & Results Analysis <component/report.rst>
   Online Serving: Online Management & Strategy & Tool <component/online.rst>
   Reinforcement Learning <component/rl/toctree>

.. toctree::
   :maxdepth: 3
   :caption: OTHER COMPONENTS/FEATURES/TOPICS:

   Building Formulaic Alphas <advanced/alpha.rst>
   Online & Offline mode <advanced/server.rst>
   Serialization <advanced/serial.rst>
   Task Management <advanced/task_management.rst>
   Point-In-Time database <advanced/PIT.rst>

.. toctree::
   :maxdepth: 3
   :caption: FOR DEVELOPERS:

   Code Standard & Development Guidance <developer/code_standard_and_dev_guide.rst>
   How to build image <developer/how_to_build_image.rst>

.. toctree::
   :maxdepth: 3
   :caption: REFERENCE:

   API <reference/api.rst>

.. toctree::
   :maxdepth: 3

   FAQ <FAQ/FAQ.rst>

.. toctree::
   :maxdepth: 3
   :caption: Change Log:

   Change Log <changelog/changelog.rst>
