qlib_init:
    provider_uri: "~/.qlib/qlib_data/cn_data"
    region: cn
market: &market csi500
benchmark: &benchmark SH000905
data_handler_config: &data_handler_config
    start_time: 2008-01-01
    end_time: 2020-08-01
    fit_start_time: 2008-01-01
    fit_end_time: 2014-12-31
    instruments: *market
    infer_processors: [
        {
            "class" : "DropCol", 
            "kwargs":{"col_list": ["VWAP0"]}
        },
        {
             "class" : "CSZFillna", 
             "kwargs":{"fields_group": "feature"}
        }
    ]
    learn_processors: [
        {
            "class" : "DropCol", 
            "kwargs":{"col_list": ["VWAP0"]}
        },
        {
            "class" : "DropnaProcessor", 
            "kwargs":{"fields_group": "feature"}
        },
        "DropnaLabel",
        {
            "class": "CSZScoreNorm", 
            "kwargs": {"fields_group": "label"}
        }
    ]
    process_type: "independent"

port_analysis_config: &port_analysis_config
    strategy:
        class: TopkDropoutStrategy
        module_path: qlib.contrib.strategy
        kwargs:
            signal: <PRED>
            topk: 50
            n_drop: 5
    backtest:
        start_time: 2017-01-01
        end_time: 2020-08-01
        account: *********
        benchmark: *benchmark
        exchange_kwargs:
            limit_threshold: 0.095
            deal_price: close
            open_cost: 0.0005
            close_cost: 0.0015
            min_cost: 5
task:
    model:
        class: DNNModelPytorch
        module_path: qlib.contrib.model.pytorch_nn
        kwargs:
            loss: mse
            lr: 0.002
            optimizer: adam
            max_steps: 8000
            batch_size: 8192
            GPU: 0
            weight_decay: 0.0002
            pt_model_kwargs:
              input_dim: 157
    dataset:
        class: DatasetH
        module_path: qlib.data.dataset
        kwargs:
            handler:
                class: Alpha158
                module_path: qlib.contrib.data.handler
                kwargs: *data_handler_config
            segments:
                train: [2008-01-01, 2014-12-31]
                valid: [2015-01-01, 2016-12-31]
                test: [2017-01-01, 2020-08-01]
    record: 
        - class: SignalRecord
          module_path: qlib.workflow.record_temp
          kwargs: 
            model: <MODEL>
            dataset: <DATASET>
        - class: SigAnaRecord
          module_path: qlib.workflow.record_temp
          kwargs: 
            ana_long_short: False
            ann_scaler: 252
        - class: PortAnaRecord
          module_path: qlib.workflow.record_temp
          kwargs: 
            config: *port_analysis_config
