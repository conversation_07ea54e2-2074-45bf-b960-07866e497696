# This workflows will upload a Python Package using Twine when a release is created
# For more information see: https://help.github.com/en/actions/language-and-framework-guides/using-python-with-github-actions#publishing-to-package-registries

name: Upload Python Package

on:
  release:
    types: [published]

jobs:
  deploy_with_bdist_wheel:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-latest, macos-13, macos-latest]
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12"]
        exclude:
          - os: macos-13
            python-version: "3.11"
          - os: macos-13
            python-version: "3.12"

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        make dev
    - name: Build wheel on ${{ matrix.os }}
      run: |
        make build
    - name: Upload to PyPi
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_TOKEN }}
      run: |
        twine check dist/*.whl
        twine upload dist/*.whl --verbose

  deploy_with_manylinux:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    - name: Build wheel on Linux
      uses: RalfG/python-wheels-manylinux-build@v0.7.1-manylinux2014_x86_64
      with:
        python-versions: 'cp38-cp38 cp39-cp39 cp310-cp310 cp311-cp311 cp312-cp312'
        build-requirements: 'numpy cython'
    - name: Install dependencies
      run: |
        python -m pip install twine
    - name: Upload to PyPi
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_TOKEN }}
      run: |
        twine check dist/pyqlib-*-manylinux*.whl
        twine upload dist/pyqlib-*-manylinux*.whl --verbose
